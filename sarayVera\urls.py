from django.urls import path, include
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.http import HttpResponse

urlpatterns = [
    path('users/', include('users.urls')),  
    path('contacts/', include('contacts.urls')),
    path('locations/', include('locations.urls')),
    path('reservations/', include('reservations.urls')),
    path('contracts/', include('contracts.urls')),
    path('expenses/', include('expenses.urls')),
    path('income/', include('income.urls')),
    path('notifications/', include('notifications.urls')),
    # make a health check path 
    path('health/', lambda request: HttpResponse(status=200), name='health_check'),
]

