from rest_framework import serializers
from django.utils import timezone
from expenses.models.expensesEvents import Event, EventHistory, EventType
from contacts.serializers.contacts import ContactSimpleSerializer
from locations.serializers.locations import LocationSimpleSerializer , LocationMinimalSerializer
from users.serializers.users import UserDetailSerializer
from contacts.models.contacts import Contact
from locations.models.locations import Location
from contracts.serializers.contracts import ContractSimpleSerializer

class EventTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventType
        fields = ['id', 'name', 'created_at']
        read_only_fields = ['id', 'created_at']

class EventHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = EventHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class EventCreateSerializer(serializers.ModelSerializer):
    contact_id = serializers.UUIDField(required=False, write_only=True, allow_null=True)
    location_id = serializers.UUIDField(required=False, write_only=True, allow_null=True)
    type_id = serializers.UUIDField(required=False, allow_null=True)
    parent_id = serializers.UUIDField(required=False, allow_null=True)
    reservation_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    income_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    contract_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    class Meta:
        model = Event
        fields = [
            'title', 'amount', 'due_date', 'paid_date', 'income_id',
            'description', 'status', 'priority', 'type_id',
            'contact_id', 'location_id', 'is_deleted', 'parent_id',
            'reservation_id', 'contract_id'
        ]
        extra_kwargs = {
            'title': {'required': True},
            'amount': {'required': True},
            'due_date': {'required': True}
        }

    def validate(self, data):
        # Validate contact
        contact_id = data.pop('contact_id', None)
        if contact_id:
            try:
                contact = Contact.objects.get(pk=contact_id)
                if contact.is_deleted:
                    raise serializers.ValidationError({"contact_id": f"Contact with ID {contact_id} has been deleted"})
                data['contact'] = contact
            except Contact.DoesNotExist:
                raise serializers.ValidationError({"contact_id": f"Contact with ID {contact_id} does not exist"})

        # Validate location
        location_id = data.pop('location_id', None)
        if location_id:
            try:
                location = Location.objects.get(pk=location_id)
                if location.is_deleted:
                    raise serializers.ValidationError({"location_id": f"Location with ID {location_id} has been deleted"})
                data['location'] = location
            except Location.DoesNotExist:
                raise serializers.ValidationError({"location_id": f"Location with ID {location_id} does not exist"})

        # Validate event type
        type_id = data.pop('type_id', None)
        if type_id:
            try:
                data['type'] = EventType.objects.get(pk=type_id)
            except EventType.DoesNotExist:
                raise serializers.ValidationError({"type_id": "Event type does not exist"})

        # Validate parent event
        parent_id = data.pop('parent_id', None)
        if parent_id:
            try:
                from expenses.models.parentEvents import ParentEvent
                data['parent'] = ParentEvent.objects.get(pk=parent_id)
            except ParentEvent.DoesNotExist:
                raise serializers.ValidationError({"parent_id": "Parent event does not exist"})

        # Validate reservation
        reservation_id = data.pop('reservation_id', None)
        if reservation_id:
            try:
                from reservations.models import Reservation
                data['reservation'] = Reservation.objects.get(pk=reservation_id)
            except Reservation.DoesNotExist:
                raise serializers.ValidationError({"reservation_id": "Reservation does not exist"})
        
        # Validate income
        income_id = data.pop('income_id', None)
        if income_id:
            try:
                from income.models.incomeEvents import Income
                data['income'] = Income.objects.get(pk=income_id)
            except Income.DoesNotExist:
                raise serializers.ValidationError({"income_id": "Income does not exist"})
        # Validate dates and status
        if 'paid_date' in data and data.get('paid_date') is not None:
            if 'status' not in data:
                data['status'] = Event.EventStatus.COMPLETED
            elif data.get('status') == Event.EventStatus.CANCELLED:
                raise serializers.ValidationError({"paid_date": "Cannot set paid date for a cancelled event"})

        #validte contract
        contract_id = data.pop('contract_id', None)
        if contract_id:
            try:
                from contracts.models.contracts import Contract
                data['contract'] = Contract.objects.get(pk=contract_id)
            except Contract.DoesNotExist:
                raise serializers.ValidationError({"contract_id": "Contract does not exist"})

        return data

    def create(self, validated_data):
        return Event.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for field, value in validated_data.items():
            setattr(instance, field, value)
        instance.save()
        return instance

class EventSimpleSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type = serializers.CharField(source='type.name', read_only=True)
    contact = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    lastEdited = serializers.SerializerMethodField()
    lastEditedBy = serializers.SerializerMethodField()
    category = serializers.CharField(default="expense", read_only=True)
    dueDate = serializers.SerializerMethodField() 
    actual_amount = serializers.SerializerMethodField()
    income_id = serializers.UUIDField(source='income.id', read_only=True)
    contract_id = serializers.UUIDField(source='contract.id', read_only=True)
    reservation_id = serializers.UUIDField(source='reservation.id', read_only=True)

    class Meta:
        model = Event
        fields = [
            'id', 'title', 'amount', 'actual_amount', 'dueDate', 'paid_date',
            'status', 'status_display', 'priority', 'priority_display',
            'type', 'is_deleted', 'contact', 'location', 'lastEdited', 'lastEditedBy',
            'category', 'contract_id' , 'reservation_id', 'created_at', 'income_id'
        ]
        read_only_fields = fields

    def get_dueDate(self, obj):
        if obj.due_date:
            return obj.due_date.date()
        return None

    def get_contact(self, obj):
        # Directly use the single foreign key contact (already prefetched)
        contact = obj.contact
        if contact:
            return {
                "id": contact.id,
                "name": contact.name,
                "email": contact.email
            }
        return None

    def get_location(self, obj):
        # Directly use the single foreign key location (already prefetched)
        location = obj.location
        if location:
            # Use cached values if they exist (avoids database queries)
            if hasattr(location, '_ownership_processed'):
                return {
                    "id": location.id,
                    "name": location.name,
                    "address": location.address,
                    "areWeOwner": location._has_primary_owner
                }
            # Fallback to method calls (will still use prefetched data)
            return {
                "id": location.id,
                "name": location.name,
                "address": location.address,
                "areWeOwner": location.are_we_owners()
            }
        return None

    def get_lastEdited(self, obj):
        if hasattr(obj, 'prefetched_history') and obj.prefetched_history:
            return obj.prefetched_history[0]['modified_at']
        elif self.context.get('prefetched_data'):
            return None
        history = obj.history.last()
        if history:
            return history.modified_at.strftime("%Y-%m-%d %H:%M:%S")
        return None

    def get_lastEditedBy(self, obj):
        if hasattr(obj, 'prefetched_history') and obj.prefetched_history:
            history_entry = obj.prefetched_history[0]
            return {
                'username': history_entry['modified_by'],
                'user': history_entry.get('user')
            }
        elif self.context.get('prefetched_data'):
            return None
        history = obj.history.last()
        if history and history.modified_by:
            return {'username': history.modified_by.username}
        return None
    def get_actual_amount(self, obj):
        if obj.location:
            # Use cached percentage if it exists (avoids database queries)
            if hasattr(obj.location, '_ownership_processed'):
                if not obj.location._has_primary_owner:
                    from decimal import Decimal
                    return str(obj.amount * Decimal(obj.location._our_percentage / 100))
            # Fallback to method calls (will still use prefetched data)
            elif not obj.location.are_we_owners():
                from decimal import Decimal
                return str(obj.amount * Decimal(obj.location.get_our_Percentage() / 100))
        return obj.amount

class EventDetailSerializer(serializers.ModelSerializer):
    contacts = ContactSimpleSerializer(many=True, read_only=True)
    locations = LocationMinimalSerializer(many=True, read_only=True)
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    history = EventHistorySerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type = EventTypeSerializer(read_only=True)
    category = serializers.SerializerMethodField()
    contract = ContractSimpleSerializer(read_only=True)
    parent_title = serializers.CharField(source='parent.title', read_only=True)
    parent_id = serializers.UUIDField(source='parent.id', read_only=True)
    actual_amount = serializers.SerializerMethodField()
    class Meta:
        model = Event
        fields = [
            'id', 'category', 'title', 'amount', 'actual_amount' , 'due_date', 'paid_date',
            'description', 'status', 'status_display', 
            'priority', 'priority_display', 'type', 'contacts', 'locations',
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'is_deleted', 'history', 'contract', 'parent_id', 'parent_title'
        ]
        read_only_fields = fields
    
    def get_category(self, obj):
        return "expense"
    
    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.amount * Decimal(obj.location.get_our_Percentage()))
        return obj.amount
