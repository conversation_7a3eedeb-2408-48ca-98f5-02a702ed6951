from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db.models import Q
from expenses.models.parentEvents import ParentEvent
from expenses.models.expensesEvents import Event
from expenses.serializers.parentEvents import ParentEventDetailSerializer
from expenses.serializers.expenses import EventCreateSerializer, EventDetailSerializer , EventTypeSerializer , EventHistorySerializer, EventSimpleSerializer
from expenses.models.expensesEvents import EventHistory
from expenses.models.expensesEvents import EventType
from django.db import transaction
from django.utils import timezone
from django.db.models import Prefetch
from django.db.models import OuterRef, Subquery
from income.serializers.income import IncomeCreateSerializer
from locations.models import Location 
from contacts.models import OurCompany
from income.models.incomeEvents import Income
from notifications.utils import create_system_notification, delete_notifications_by_category
from django.utils.dateparse import parse_datetime
import uuid
from sarayVera.settings import numOfQueriesWraper

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_all_expenses(request):
    """
    Retrieve all expenses (parent events with children and normal events) sorted by date.
    """
    try:
        # Load all child events directly, avoiding subqueries
        all_child_events = {}

        # Get all parent events
        parent_events = ParentEvent.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')

        parent_ids = list(parent_events.values_list('id', flat=True))

        # Get all events in one query, using select_related and prefetch_related
        all_events = Event.objects.filter(
            Q(parent__in=parent_ids, is_deleted=False) |
            Q(parent__isnull=True, is_deleted=False)
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'contact', 'location', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares'  # Prefetch the ownership shares
        )

        # Separate child events and normal events
        normal_events = []
        for event in all_events:
            if event.parent_id:
                all_child_events.setdefault(event.parent_id, []).append(event)
            else:
                normal_events.append(event)

        all_event_ids = [event.id for event in all_events]

        # Prefetch all history in one query with raw SQL
        from django.db import connection
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT eh.event_id, eh.modified_at, eh.id AS history_id, u.id AS user_id, u.username, u.email, u.role
                FROM expenses_eventhistory eh
                LEFT JOIN users_user u ON eh.modified_by_id = u.id
                WHERE eh.event_id = ANY(%s)
                ORDER BY eh.modified_at DESC
            """, [all_event_ids])
            for row in cursor.fetchall():
                event_id, modified_at, history_id, user_id, username, user_email, user_role = row
                history_map.setdefault(event_id, []).append({
                    'id': history_id,
                    'modified_at': modified_at,
                    'modified_by': username,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'email': user_email,
                        'role': user_role
                    } if user_id else None
                })

        # Pre-process location ownership data
        for event in all_events:
            if event.location:
                # Cache calculated values on the location object
                location = event.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Attach child events + prefetched history
        for parent in parent_events:
            parent.prefetched_child_events = all_child_events.get(parent.id, [])
            parent.first_child_due_date = (
                min((c.due_date for c in parent.prefetched_child_events), default=None)
            )
            for child in parent.prefetched_child_events:
                child.prefetched_history = history_map.get(child.id, [])

        # Attach history to normal events
        for event in normal_events:
            event.prefetched_history = history_map.get(event.id, [])

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        
        # Serialize
        context = {'prefetched_data': True}
        parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
        normal_serializer = EventSimpleSerializer(normal_events, many=True, context=context)

        # Unpatch methods
        LocationPatch.unpatch(originals)
        
        combined_expenses = parent_serializer.data + normal_serializer.data
        combined_expenses.sort(key=lambda x: x.get('created_at', ''))

        return Response({"expenses": combined_expenses, "count": len(combined_expenses)}, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to retrieve expenses", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_events(request):
    created_events = []
    errors = []

    events_data = request.data.get("events", [])

    if not isinstance(events_data, list) or not events_data:
        return Response({"error": "Invalid or empty 'events' list."}, status=status.HTTP_400_BAD_REQUEST)

    for index, event_data in enumerate(events_data):
        try:
            with transaction.atomic():
                # Create a copy of the data to modify if needed
                event_data_copy = event_data.copy()
                
                # Check if we should use percentage-based calculation
                # Accept both spellings for backward compatibility
                isTotalAmount = event_data_copy.get("isTotalAmount", False) or event_data_copy.get("is_percatage", False)
                
                if isTotalAmount and event_data_copy.get("location_id"):
                    location_id = event_data_copy.get("location_id")
                    try:
                        location = Location.objects.prefetch_related("ownership_shares").get(id=location_id)
                        # Get our company's share percentage
                        our_share = location.ownership_shares.filter(our_company=True).first()
                        
                        if our_share:
                            original_amount = float(event_data_copy.get("amount", 0))
                            percentage = float(our_share.percentage) / 100.0  # Convert percentage to decimal
                            # Adjust the amount based on our percentage
                            calculated_amount = round(original_amount * percentage, 2)
                            print(f"Percentage calculation: {original_amount} * {our_share.percentage}% = {calculated_amount}")
                            event_data_copy["amount"] = calculated_amount
                        else:
                            print(f"No company share found for location {location_id}")
                    except Exception as percentage_error:
                        errors.append({
                            "index": index,
                            "error": "Failed to calculate percentage-based amount",
                            "details": str(percentage_error)
                        })
                        continue
                
                serializer = EventCreateSerializer(data=event_data_copy)
                if not serializer.is_valid():
                    errors.append({
                        "index": index,
                        "error": "Invalid data",
                        "details": serializer.errors
                    })
                    continue

                event = serializer.save(
                    created_by=request.user,
                    updated_by=request.user
                )

                # Create system notification for the expense
                create_system_notification(
                    title=f"New Expense Created: {event.title}",
                    title_ar=f"تم إنشاء مصروف جديد: {event.title}",
                    message=f"A new expense '{event.title}' for {event.amount} was created by {request.user.email} and the due date is {event.due_date}.",
                    message_ar=f"تم إنشاء مصروف جديد '{event.title}' بمبلغ {event.amount} بواسطة {request.user.email} وتاريخ الاستحقاق هو {event.due_date}.",
                    priority="medium",
                    notification_type_name="Expense Created",
                    category="expense",
                    category_id=str(event.id)
                )

                notification_view_date = event_data_copy.get('notification_date')
                if notification_view_date:
                    try:
                        notification_date = parse_datetime(notification_view_date)
                        if notification_date:
                            days_difference = (event.due_date - notification_date).days if notification_date < event.due_date else 0
                            reminder_message = f"You should pay {event.amount} in {days_difference} days." if days_difference > 0 else f"You need to pay {event.amount} today or it will be overdue."
                            reminder_message_ar = f"يجب عليك دفع {event.amount} في {days_difference} يومًا." if days_difference > 0 else f"يجب عليك دفع {event.amount} اليوم أو سيتأخر."
                            create_system_notification(
                                title=f"Upcoming Expense Due: {event.title}",
                                title_ar=f"استحقاق مصروف قريب: {event.title}",
                                message=reminder_message,
                                message_ar=reminder_message_ar,
                                priority="medium",
                                notification_type_name="Expense Reminder",
                                view_date=notification_date,
                                category="expense",
                                category_id=str(event.id)
                            )
                    except Exception as date_error:
                        print(f"Error processing notification date for event index {index}: {str(date_error)}")

                if event_data_copy.get("autoCreate", True) is True:
                    location_id = event_data_copy.get("location_id")
                    if not location_id:
                        raise Exception("Location ID is required for auto income creation.")

                    location = Location.objects.prefetch_related("ownership_shares__contact").get(id=location_id)
                    if location.are_we_owners():
                        our_id = OurCompany.get_instance().id
                        for share in location.ownership_shares.all():
                            if share.contact.id == our_id:
                                continue
                            percentage = float(share.percentage)
                            income_amount = round((percentage / 100.0) * float(event.amount), 2)
                            income_data = {
                                "title": f"{event.title} - Share of {share.contact.name}",
                                "amount": income_amount,
                                "due_date": event.due_date,
                                "description": f"Auto-created income for {share.contact.name} from event {event.id}",
                                "status": "upcoming",
                                "priority": "medium",
                                "contact_id": str(share.contact.id),
                                "location_id": str(location.id),
                                "reservation_id": event_data_copy.get("reservation_id"),
                                "parent_id": event_data_copy.get("parent_id"),
                                "contract_id": event_data_copy.get("contract_id") if event_data_copy.get("contract_id") else None
                            }

                            income_serializer = IncomeCreateSerializer(data=income_data)
                            if income_serializer.is_valid():
                                income_serializer.save(created_by=request.user, updated_by=request.user)
                            else:
                                raise Exception({
                                    "auto_created_income_error": income_serializer.errors,
                                    "contact": share.contact.id
                                })

                detail_serializer = EventDetailSerializer(event)
                created_events.append(detail_serializer.data)

        except Exception as e:
            errors.append({
                "index": index,
                "error": "Failed to create event",
                "details": str(e)
            })

    status_code = status.HTTP_207_MULTI_STATUS if errors else status.HTTP_201_CREATED
    return Response({
        "created_events": created_events,
        "errors": errors
    }, status=status_code)

def create_contracts_events(event_data, user , contract):
    try:
        print("Starting create_multiple_events function")
        with transaction.atomic():
            print("Transaction started")
            # Create a copy of the data to modify if needed
            event_data_copy = event_data.copy()
            print(f"Event data copy created: {event_data_copy}")
            
            isTotalAmount = event_data_copy.get("isTotalAmount", False) or event_data_copy.get("is_percatage", False)
            print(f"isTotalAmount flag: {isTotalAmount}")
            
            if isTotalAmount and event_data_copy.get("location_id"):
                location_id = event_data_copy.get("location_id")
                print(f"Location ID: {location_id}")
                try:
                    location = Location.objects.prefetch_related("ownership_shares").get(id=location_id)
                    print(f"Location fetched: {location}")
                    # Get our company's share percentage
                    our_share = location.ownership_shares.filter(our_company=True).first()
                    print(f"Our share: {our_share}")
                    
                    if our_share:
                        original_amount = float(event_data_copy.get("amount", 0))
                        percentage = float(our_share.percentage) / 100.0  # Convert percentage to decimal
                        # Adjust the amount based on our percentage
                        calculated_amount = round(original_amount * percentage, 2)
                        print(f"Percentage calculation: {original_amount} * {our_share.percentage}% = {calculated_amount}")
                        event_data_copy["amount"] = calculated_amount
                    else:
                        print(f"No company share found for location {location_id}")
                except Exception as percentage_error:
                    print(f"Error calculating percentage-based amount: {percentage_error}")
                    raise Exception({
                        "error": "Failed to calculate percentage-based amount",
                        "details": str(percentage_error)
                    })
            
            print(f"Final event data before serialization: {event_data_copy}")
            serializer = EventCreateSerializer(data=event_data_copy)
            if not serializer.is_valid():
                print(f"Serializer errors: {serializer.errors}")
                raise Exception({
                    "error": "Invalid data",
                    "details": serializer.errors
                })

            serializer.contract = contract
            event = serializer.save(
                created_by=user,
                updated_by=user
            )
            print(f"Event created: {event}")

            # Create system notification for the expense
            create_system_notification(
                title=f"New Expense Created: {event.title}",
                title_ar=f"تم إنشاء مصروف جديد: {event.title}",
                message=f"A new expense '{event.title}' for {event.amount} was created by {user.email} and the due date is {event.due_date}.",
                message_ar=f"تم إنشاء مصروف جديد '{event.title}' بمبلغ {event.amount} بواسطة {user.email} وتاريخ الاستحقاق هو {event.due_date}.",
                view_date= event.view_date if hasattr(event, 'view_date') else None,
                priority="medium",
                notification_type_name="Expense Created",
                category="expense",
                category_id=str(event.id)  # Use str to ensure UUID is serialized correctly
            )
            print(f"System notification created for event: {event.title}")
            
            notification_view_date = event_data_copy.get('notification_date')
            if notification_view_date:
                try:
                    notification_date = parse_datetime(notification_view_date)
                    print(f"Notification date parsed: {notification_date}")
                    if notification_date:
                        # Ensure both dates are timezone-aware for comparison
                        if notification_date.tzinfo is None:
                            notification_date = timezone.make_aware(notification_date)
                        
                        event_due_date = event.due_date
                        if hasattr(event_due_date, 'tzinfo') and event_due_date.tzinfo is None:
                            event_due_date = timezone.make_aware(event_due_date)
                        elif not hasattr(event_due_date, 'tzinfo'):
                            # If it's a date object, convert to datetime and make aware
                            from datetime import datetime
                            event_due_date = timezone.make_aware(datetime.combine(event_due_date, datetime.min.time()))
                        
                        days_difference = (event_due_date - notification_date).days if notification_date < event_due_date else 0
                        reminder_message = f"You should pay {event.amount} in {days_difference} days." if days_difference > 0 else f"You need to pay {event.amount} today or it will be overdue."
                        reminder_message_ar = f"يجب عليك دفع {event.amount} في {days_difference} يومًا." if days_difference > 0 else f"يجب عليك دفع {event.amount} اليوم أو سيتأخر."
                        create_system_notification(
                            title=f"Upcoming Expense Due: {event.title}",
                            title_ar=f"استحقاق مصروف قريب: {event.title}",
                            message=reminder_message,
                            message_ar=reminder_message_ar,
                            priority="medium",
                            notification_type_name="Expense Reminder",
                            view_date=notification_date,
                            category="expense_due_date",  # Use a specific category for due date reminders
                            category_id=str(event.id)  # Use str to ensure UUID is serialized correctly
                        )
                        print(f"Reminder notification created for event: {event.title}")
                except Exception as date_error:
                    print(f"Error processing notification date: {date_error}")

            if event_data_copy.get("autoCreate", True) is True:
                location_id = event_data_copy.get("location_id")
                print(f"Auto-create flag is True. Location ID: {location_id}")
                if not location_id:
                    raise Exception("Location ID is required for auto income creation.")

                location = Location.objects.prefetch_related("ownership_shares__contact").get(id=location_id)
                print(f"Location fetched for auto income creation: {location}")
                if location.are_we_owners():
                    print(f"We are owners of the location: {location}")
                    our_id = OurCompany.get_instance().id
                    for share in location.ownership_shares.all():
                        if share.contact.id == our_id:
                            print(f"Skipping our own share: {share}")
                            continue
                        percentage = float(share.percentage)
                        income_amount = round((percentage / 100.0) * float(event.amount), 2)
                        income_data = {
                            "title": f"{event.title} - Share of {share.contact.name}",
                            "amount": income_amount,
                            "due_date": event.due_date,
                            "description": f"Auto-created income for {share.contact.name} from event {event.id}",
                            "status": "upcoming",
                            "expense_id" : str(event.id),
                            "priority": "medium",
                            "contact_id": str(share.contact.id),
                            "location_id": str(location.id),
                            "reservation_id": event_data_copy.get("reservation_id"),
                            "parent_id": event_data_copy.get("parent_id"),
                            "contract_id": str(contract.id)  # Add contract_id to the data instead of setting it later
                        }
                        print(f"Income data prepared: {income_data}")
                        
                        income_serializer = IncomeCreateSerializer(data=income_data)
                        if income_serializer.is_valid():
                            # Don't set income_serializer.contract - pass it in the save method
                            created_income = income_serializer.save(created_by=user, updated_by=user)
                            # Link to contract after creation
                            created_income.contract = contract
                            created_income.save()
                            print(f"Income created for contact: {share.contact.name}")
                            
                            # Create notification for the auto-created income
                            create_system_notification(
                                title=f"Auto-Generated Income Created: {created_income.title} for expense {event.title}",
                                title_ar=f"تم إنشاء دخل تلقائي: {created_income.title} للمصروف {event.title}",
                                message=f"An income '{created_income.title}' for {created_income.amount} was auto-created for {share.contact.name} based on their {percentage}% share from expense '{event.title}' in contract '{contract.title}'",
                                message_ar=f"تم إنشاء دخل تلقائي '{created_income.title}' بمبلغ {created_income.amount} لـ {share.contact.name} بناءً على حصتهم {percentage}% من المصروف '{event.title}' في العقد '{contract.title}'",
                                priority="low",
                                view_date=created_income.view_date if hasattr(created_income, 'view_date') else None,
                                notification_type_name="Auto-Generated Income",
                                category="income",
                                category_id=str(created_income.id)
                            )
                            print(f"Notification created for auto-generated income: {created_income.title}")
                            
                            # Create reminder notification if due date is in the future - FIXED TIMEZONE COMPARISON
                            if created_income.due_date:
                                # Convert both to the same type for comparison
                                income_due_date = created_income.due_date
                                current_date = timezone.now().date()
                                
                                # If income_due_date is a datetime, extract the date part
                                if hasattr(income_due_date, 'date'):
                                    income_due_date = income_due_date.date()
                                
                                # Now both are date objects, safe to compare
                                if income_due_date > current_date:
                                    days_until_due = (income_due_date - current_date).days
                                    if days_until_due > 0:
                                        reminder_message = f"Income of {created_income.amount} from {share.contact.name} is due in {days_until_due} days for their share of '{event.title}'"
                                        reminder_message_ar = f"دخل بمبلغ {created_income.amount} من {share.contact.name} مستحق خلال {days_until_due} يومًا لحصتهم من '{event.title}'"
                                        
                                        # Calculate view_date (3 days before due date, but not in the past)
                                        view_date = timezone.now() + timezone.timedelta(days=max(0, days_until_due-3))
                                        
                                        create_system_notification(
                                            title=f"Upcoming Income Due: {created_income.title}",
                                            title_ar=f"دخل مستحق قريبًا: {created_income.title}",
                                            message=reminder_message,
                                            message_ar=reminder_message_ar,
                                            priority="medium",
                                            notification_type_name="Income Due Reminder",
                                            view_date=view_date,
                                            category="income_due_date",
                                            category_id=str(created_income.id)
                                        )
                                        print(f"Due date reminder notification created for income: {created_income.title}")
                        else:
                            print(f"Income serializer errors: {income_serializer.errors}")
                            raise Exception({
                                "auto_created_income_error": income_serializer.errors,
                                "contact": share.contact.id
                            })

            detail_serializer = EventDetailSerializer(event)
            print(f"Event detail serialized: {detail_serializer.data}")

    except Exception as e:
        print(f"Exception occurred: {e}")
        raise Exception(
            {"error": "Failed to create event", "details": str(e)}
        )
    print("create_multiple_events function completed successfully")
    return detail_serializer.data

    

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_event(request):
    try:
        with transaction.atomic():
            request_data = request.data.copy()
            
            # Check if we should use percentage-based calculation
            isTotalAmount = request_data.get("isTotalAmount", False)
            
            if isTotalAmount and request_data.get("location_id"):
                location_id = request_data.get("location_id")
                try:
                    location = Location.objects.prefetch_related("ownership_shares").get(id=location_id)
                    # Get our company's share percentage
                    our_share = location.ownership_shares.filter(our_company=True).first()
                    
                    if our_share:
                        original_amount = float(request_data.get("amount", 0))
                        percentage = float(our_share.percentage) / 100.0  # Convert percentage to decimal
                        # Adjust the amount based on our percentage
                        calculated_amount = round(original_amount * percentage, 2)
                        print(f"Percentage calculation: {original_amount} * {our_share.percentage}% = {calculated_amount}")
                        request_data["amount"] = calculated_amount
                    else:
                        print("No company share found for this location")
                except Exception as percentage_error:
                    return Response(
                        {"error": "Failed to calculate percentage-based amount", 
                         "details": str(percentage_error)},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            serializer = EventCreateSerializer(data=request_data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            event = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )

            # Create system notification for the expense
            create_system_notification(
                title=f"New Expense Created: {event.title}",
                title_ar=f"تم إنشاء مصروف جديد: {event.title}",
                message=f"A new expense '{event.title}' for {event.amount} was created by {request.user.email} and the due date is {event.due_date}.",
                message_ar=f"تم إنشاء مصروف جديد '{event.title}' بمبلغ {event.amount} بواسطة {request.user.email} وتاريخ الاستحقاق هو {event.due_date}.",
                priority="medium",
                notification_type_name="Expense Created",
                category="expense",
                category_id=str(event.id)  # Use str to ensure UUID is serialized correctly
            )

            notification_view_date = request_data.get('notification_date')
            if notification_view_date:
                try:
                    notification_date = parse_datetime(notification_view_date)
                    if notification_date:
                        days_difference = (event.due_date - notification_date).days if notification_date < event.due_date else 0
                        reminder_message = f"You should pay {event.amount} in {days_difference} days." if days_difference > 0 else f"You need to pay {event.amount} today or it will be overdue."
                        reminder_message_ar = f"يجب عليك دفع {event.amount} في {days_difference} يومًا." if days_difference > 0 else f"يجب عليك دفع {event.amount} اليوم أو سيتأخر."
                        create_system_notification(
                            title=f"Upcoming Expense Due: {event.title}",
                            title_ar=f"استحقاق مصروف قريب: {event.title}",
                            message=reminder_message,
                            message_ar=reminder_message_ar,
                            priority="medium",
                            notification_type_name="Expense Reminder",
                            view_date=notification_date,
                            category="expense",
                            category_id=str(event.id)  # Use str to ensure UUID is serialized correctly
                        )
                except Exception as date_error:
                    print(f"Error processing notification date: {str(date_error)}")

            if request_data.get("autoCreate", True) is True:
           

                location_id = request_data.get("location_id")
                if not location_id:
                    raise Exception("Location ID is required for auto income creation.")

                location = Location.objects.prefetch_related("ownership_shares__contact").get(id=location_id)
                if(location.are_we_owners()):
                    our_id = OurCompany.get_instance().id
                    for share in location.ownership_shares.all():
                        if (share.contact.id == our_id):
                            continue
                        percentage = float(share.percentage)
                        income_amount = round((percentage / 100.0) * float(event.amount), 2)
                        income_data = {
                            "title": f"{event.title} - Share of {share.contact.name}",
                            "amount": income_amount,
                            "due_date": event.due_date,
                            "description": f"Auto-created income for {share.contact.name} from event {event.id}",
                            "status": "upcoming",
                            "priority": "medium",
                            "contact_id": str(share.contact.id),
                            "location_id": str(location.id),
                            "reservation_id": request_data.get("reservation_id"),
                            "parent_id": request_data.get("parent_id")
                        }

                        income_serializer = IncomeCreateSerializer(data=income_data)
                        if income_serializer.is_valid():
                            income_serializer.save(created_by=request.user, updated_by=request.user)
                        else:
                            raise Exception({
                                "auto_created_income_error": income_serializer.errors,
                                "contact": share.contact.id
                            })

            detail_serializer = EventDetailSerializer(event)
            return Response(detail_serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response(
            {"error": "Failed to create event", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_events(request):
    try:
        print('s;df')
        # Filter out soft-deleted events by default
        include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'

        # Start with a base queryset with select_related for foreign keys
        events = Event.objects.select_related(
            'type', 'created_by', 'updated_by', 'parent', 'contract', 'contact', 'location', 'reservation', 'income'
        )

        if not include_deleted:
            events = events.filter(is_deleted=False)

        # Filter by status
        status_param = request.query_params.get('status')
        if status_param:
            events = events.filter(status=status_param)

        # Filter by priority
        priority = request.query_params.get('priority')
        if priority:
            events = events.filter(priority=priority)

        # Filter by date range
        due_before = request.query_params.get('due_before')
        if due_before:
            events = events.filter(due_date__lte=due_before)

        due_after = request.query_params.get('due_after')
        if due_after:
            events = events.filter(due_date__gte=due_after)

        # Filter by single contact and location
        contact_id = request.query_params.get('contact_id')
        if contact_id:
            events = events.filter(contact__id=contact_id)

        location_id = request.query_params.get('location_id')
        if location_id:
            events = events.filter(location__id=location_id)

        # Search by title or description
        search = request.query_params.get('search')
        if search:
            events = events.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search)
            )

        # Order by due date
        events = events.order_by('due_date')

        if not events.exists():
            return Response({"events": [], "count": 0}, status=status.HTTP_200_OK)

        events = list(events)
        all_event_ids = [event.id for event in events]

        # Prefetch all history in one query with raw SQL
        from django.db import connection
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT eh.event_id, eh.modified_at, eh.id AS history_id, u.id AS user_id, u.username, u.email, u.role
                FROM expenses_eventhistory eh
                LEFT JOIN users_user u ON eh.modified_by_id = u.id
                WHERE eh.event_id = ANY(%s)
                ORDER BY eh.modified_at DESC
            """, [all_event_ids])
            for row in cursor.fetchall():
                event_id, modified_at, history_id, user_id, username, user_email, user_role = row
                if event_id not in history_map:
                    history_map[event_id] = []
                history_map[event_id].append({
                    'id': history_id,
                    'modified_at': modified_at,
                    'modified_by': username,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'email': user_email,
                        'role': user_role
                    } if user_id else None
                })

        # Attach prefetched data to events
        for event in events:
            event.prefetched_history = history_map.get(event.id, [])

        # Create serializers with the prefetched data
        context = {'prefetched_data': True}
        serializer = EventSimpleSerializer(events, many=True, context=context)

        return Response({"events": serializer.data, "count": len(events)}, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        print(traceback.format_exc())  # For debugging
        return Response(
            {"error": "Failed to retrieve events", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_event(request, event_id):
    try:
        event = get_object_or_404(Event, id=event_id)
        serializer = EventDetailSerializer(event)
        return Response(serializer.data)
    except Event.DoesNotExist:
        return Response(
            {"error": f"Event with ID {event_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve event", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def update_event(request, event_id):
    try:
        with transaction.atomic():
            event = get_object_or_404(Event.objects.select_related('reservation', 'contract'), id=event_id)
            
            # Check if the expense is linked to a reservation
            if event.reservation_id is not None:
                reservation = event.reservation  # Already loaded via select_related
                
                return Response({
                    "error": f"Expense '{event.title}' cannot be updated because it is linked to reservation '{reservation.title}'",
                    "can_update": False,
                    "reservation": {
                        "id": str(reservation.id),
                        "title": reservation.title,
                        "start_date": reservation.start_date.isoformat() if reservation.start_date else None,
                        "end_date": reservation.end_date.isoformat() if reservation.end_date else None,
                        "status": reservation.status,
                        "contact_name": reservation.contact.name if hasattr(reservation, 'contact') and reservation.contact else None,
                    },
                    "message": f"To update this expense, edit reservation '{reservation.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Check if the expense is linked to a contract
            if event.contract_id is not None:
                contract = event.contract  # Already loaded via select_related
                
                return Response({
                    "error": f"Expense '{event.title}' cannot be updated because it is linked to contract '{contract.title}'",
                    "can_update": False,
                    "contract": {
                        "id": str(contract.id),
                        "title": contract.title,
                        "start_date": contract.start_date.isoformat() if contract.start_date else None,
                        "end_date": contract.end_date.isoformat() if contract.end_date else None,
                        "status": contract.status,
                        "contact_name": contract.contact.name if hasattr(contract, 'contact') and contract.contact else None,
                    },
                    "message": f"To update this expense, edit contract '{contract.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Expense is not linked to a reservation or contract, proceed with update
            
            # Capture the previous state before making changes
            previous_data = event.to_dict()
            
            serializer = EventCreateSerializer(
                event,
                data=request.data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update with current user as updater
            event = serializer.save(updated_by=request.user)
            
            # Create history record with the previous state
            event.create_history_record(previous_data, request.user)
            
            # Create notification for the event update
            from notifications.utils import create_system_notification
            
            # Determine what was changed
            changes = []
            current_data = event.to_dict()
            
            # Check key fields for changes
            if previous_data['amount'] != current_data['amount']:
                changes.append(f"amount changed from {previous_data['amount']} to {current_data['amount']}")
            
            if previous_data['status'] != current_data['status']:
                changes.append(f"status changed from {previous_data['status']} to {current_data['status']}")
            
            if previous_data['due_date'] != current_data['due_date']:
                changes.append(f"due date changed")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Expense '{event.title}' updated",
                title_ar=f"تم تحديث المصروف '{event.title}'",
                message=f"The expense '{event.title}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث المصروف '{event.title}' بواسطة {request.user.email}: {change_description}",
                priority="low",  # Changed from medium to low
                notification_type_name="Expense Update",
                category="expense",
                category_id=str(event.id),  # Use str to ensure UUID is serialized correctly
            )
            
            # Return detailed event info
            detail_serializer = EventDetailSerializer(event)
            return Response(detail_serializer.data)
    except Event.DoesNotExist:
        return Response(
            {"error": f"Event with ID {event_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update event", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_event(request, event_id):
    try:
        event = get_object_or_404(Event, id=event_id)
        
        # Check if we're doing a soft delete or permanent delete
        permanent = request.query_params.get('permanent', 'false').lower() == 'true'
        
        if permanent:
            # Get confirmation from request to prevent accidental deletion
            confirm = request.query_params.get('confirm', 'false').lower() == 'true'
            if not confirm:
                return Response(
                    {"warning": "This will permanently delete the event. Set confirm=true to proceed."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Save event info for response
            event_title = event.title
            
            # Delete the event and all its history (cascade)
            event.delete()
            
            return Response({
                "message": f"Event '{event_title}' has been permanently deleted"
            }, status=status.HTTP_200_OK)
        else:
            # Perform soft delete
            event.soft_delete(request.user)
            
            return Response({
                "message": f"Event '{event.title}' has been marked as deleted"
            }, status=status.HTTP_200_OK)
    except Event.DoesNotExist:
        return Response(
            {"error": f"Event with ID {event_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete event", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_event_history(request, event_id):
    """Retrieve history records for a specific event"""
    try:
        # First check if event exists
        event = get_object_or_404(Event, id=event_id)
        
        # Get all history records for this event
        history_records = EventHistory.objects.filter(event=event).order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "event_id": event_id,
                "event_title": event.title,
                "history": [],
                "message": "No history records found for this event"
            })
        
        # Serialize the history records
        serializer = EventHistorySerializer(history_records, many=True)
        
        return Response({
            "event_id": event_id,
            "event_title": event.title,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Event.DoesNotExist:
        return Response(
            {"error": f"Event with ID {event_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve event history", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_event_paid(request, event_id):
    """Mark an event as paid with the current date"""
    try:
        with transaction.atomic():
            event = get_object_or_404(Event, id=event_id)
            
            # Check if already paid
            if event.paid_date is not None:
                return Response(
                    {"error": "Event is already marked as paid"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Check if cancelled
            if event.status == Event.EventStatus.CANCELLED:
                return Response(
                    {"error": "Cannot mark a cancelled event as paid"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Capture the previous state before making changes
            previous_data = event.to_dict()
            
            # Mark as paid and completed
            event.paid_date = timezone.now()
            event.status = Event.EventStatus.COMPLETED
            event.updated_by = request.user
            event.save()
            
            # Create history record with the previous state
            event.create_history_record(previous_data, request.user)
            
            return Response({
                "message": f"Event '{event.title}' has been marked as paid",
                "event": EventDetailSerializer(event).data
            })
    except Event.DoesNotExist:
        return Response(
            {"error": f"Event with ID {event_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to mark event as paid", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_event_types(request):
    """List all event types"""
    try:
        event_types = EventType.objects.all()
        serializer = EventTypeSerializer(event_types, many=True)
        return Response({"event_types": serializer.data, "count": event_types.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve event types", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_event_type(request):
    """Create a new event type"""
    try:
        serializer = EventTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if type with same name already exists
        name = serializer.validated_data['name']
        if EventType.objects.filter(name__iexact=name).exists():
            return Response(
                {"error": f"Event type '{name}' already exists"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new event type
        event_type = serializer.save(created_by=request.user)
        
        return Response(
            EventTypeSerializer(event_type).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        return Response(
            {"error": "Failed to create event type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_event_type(request, type_id):
    """Delete an event type"""
    try:
        event_type = get_object_or_404(EventType, id=type_id)
        
        # Check if type is used by any events
        if Event.objects.filter(type=event_type).exists():
            return Response(
                {"error": f"Cannot delete event type '{event_type.name}' as it is being used by events"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Store type name for response
        type_name = event_type.name
        
        # Delete the type
        event_type.delete()
        
        return Response({
            "message": f"Event type '{type_name}' has been deleted"
        }, status=status.HTTP_200_OK)
    except EventType.DoesNotExist:
        return Response(
            {"error": f"Event type with ID {type_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete event type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def list_expenses_by_location(request):
    """
    Retrieve all expenses filtered by a specific location ID.
    POST request with location_id in the body.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Load all child events directly, avoiding subqueries
        all_child_events = {}
        
        # Get all parent events
        parent_events = ParentEvent.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')
        
        # Get IDs for further operations
        parent_ids = list(parent_events.values_list('id', flat=True))
        
        # Get all events in one query with location filter - IMPROVED PREFETCHING
        all_events = Event.objects.filter(
            (Q(parent__in=parent_ids, is_deleted=False) |  # Child events
            Q(parent__isnull=True, is_deleted=False)) &    # Normal events
            Q(location__id=location_id)                   # Filter by location
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'contact', 'location', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares',
            'location__ownership_shares__contact'
        )
        
        # Separate child events and normal events
        normal_events = []
        filtered_parent_ids = set()  # Track which parents have matching children
        
        for event in all_events:
            if event.parent_id:
                if event.parent_id not in all_child_events:
                    all_child_events[event.parent_id] = []
                all_child_events[event.parent_id].append(event)
                filtered_parent_ids.add(event.parent_id)
            else:
                normal_events.append(event)
        
        # Filter parent events to only include those with matching children
        parent_events = [parent for parent in parent_events if parent.id in filtered_parent_ids]
        
        # Get all event IDs for prefetching related data
        all_event_ids = [event.id for event in all_events]

        # Pre-process location ownership data for all events
        for event in all_events:
            if event.location:
                # Cache calculated values on the location object
                location = event.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage
        
        # Prefetch all history in one query with raw SQL
        from django.db import connection
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT eh.event_id, eh.modified_at, eh.id AS history_id, u.id AS user_id, u.username, u.email, u.role
                FROM expenses_eventhistory eh
                LEFT JOIN users_user u ON eh.modified_by_id = u.id
                WHERE eh.event_id = ANY(%s)
                ORDER BY eh.modified_at DESC
            """, [all_event_ids])
            for row in cursor.fetchall():
                event_id, modified_at, history_id, user_id, username, user_email, user_role = row
                history_map.setdefault(event_id, []).append({
                    'id': history_id,
                    'modified_at': modified_at,
                    'modified_by': username,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'email': user_email,
                        'role': user_role
                    } if user_id else None
                })
        
        # Attach the prefetched data to parent events
        for parent in parent_events:
            parent.prefetched_child_events = all_child_events.get(parent.id, [])
            # Find first child's due date for parent
            if parent.prefetched_child_events:
                parent.first_child_due_date = sorted(
                    parent.prefetched_child_events,
                    key=lambda x: x.due_date
                )[0].due_date
            else:
                parent.first_child_due_date = None
            
            # Attach prefetched data to each child event
            for child in parent.prefetched_child_events:
                child.prefetched_history = history_map.get(child.id, [])
        
        # Attach prefetched data to normal events
        for event in normal_events:
            event.prefetched_history = history_map.get(event.id, [])

        # Store original methods before patching
        original_are_we_owners = Location.are_we_owners
        original_get_our_percentage = Location.get_our_Percentage

        # Define patched methods
        def patched_are_we_owners(self):
            if hasattr(self, '_ownership_processed'):
                return self._has_primary_owner
            return original_are_we_owners(self)
        
        def patched_get_our_percentage(self):
            if hasattr(self, '_ownership_processed'):
                return self._our_percentage
            return original_get_our_percentage(self)
        
        # Apply patches
        Location.are_we_owners = patched_are_we_owners
        Location.get_our_Percentage = patched_get_our_percentage
        
        try:
            # Create serializers with the prefetched data
            context = {'prefetched_data': True}
            parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
            normal_serializer = EventSimpleSerializer(normal_events, many=True, context=context)
            
            # Combine and sort by date
            combined_expenses = parent_serializer.data + normal_serializer.data
            combined_expenses.sort(key=lambda x: x.get('created_at', ''))
            
        finally:
            # Restore original methods
            Location.are_we_owners = original_are_we_owners
            Location.get_our_Percentage = original_get_our_percentage
        
        # Add location info to response
        try:
            location = Location.objects.get(id=location_id)
            location_info = {
                "id": location_id,
                "name": location.name,
                "address": location.address
            }
        except:
            location_info = {"id": location_id}
        
        return Response({
            "location": location_info,
            "expenses": combined_expenses, 
            "count": len(combined_expenses)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve expenses by location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def list_expenses_by_contact(request):
    """
    Retrieve all expenses filtered by a specific contact ID.
    POST request with contact_id in the body.
    """
    try:
        # Get contact_id from request body
        contact_id = request.data.get('contact_id')
        if not contact_id:
            return Response(
                {"error": "contact_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Load all child events directly, avoiding subqueries
        all_child_events = {}
        
        # Get all parent events
        parent_events = ParentEvent.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')
        
        # Get IDs for further operations
        parent_ids = list(parent_events.values_list('id', flat=True))
        
        # Get all events in one query with contact filter
        all_events = Event.objects.filter(
            (Q(parent__in=parent_ids, is_deleted=False) |  # Child events
            Q(parent__isnull=True, is_deleted=False)) &    # Normal events
            Q(contact__id=contact_id)                     # Filter by contact
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'contact', 'location', 'reservation', 'contract', 'income'
        ).distinct()  # Avoid duplicates if multiple contacts match
        
        # Separate child events and normal events
        normal_events = []
        filtered_parent_ids = set()  # Track which parents have matching children
        
        for event in all_events:
            if event.parent_id:
                if event.parent_id not in all_child_events:
                    all_child_events[event.parent_id] = []
                all_child_events[event.parent_id].append(event)
                filtered_parent_ids.add(event.parent_id)
            else:
                normal_events.append(event)
        
        # Filter parent events to only include those with matching children
        parent_events = [parent for parent in parent_events if parent.id in filtered_parent_ids]
        
        # Get all event IDs for prefetching related data
        all_event_ids = [event.id for event in all_events]
        
        # Prefetch all contacts in one query with raw SQL
        from django.db import connection
        contact_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT ec.id, c.id, c.name, c.email
                FROM expenses_event ec
                JOIN contacts_contact c ON ec.contact_id = c.id
                WHERE ec.id = ANY(%s)
            """, [all_event_ids])
            for row in cursor.fetchall():
                id, contact_id, contact_name, contact_email = row
                if id not in contact_map:
                    contact_map[id] = []
                contact_map[id].append({
                    'id': contact_id,
                    'name': contact_name,
                    'email': contact_email
                })
        
        # Prefetch all locations in one query with raw SQL
        location_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT el.id, l.id, l.name, l.address 
                FROM expenses_event el
                JOIN locations_location l ON el.location_id = l.id
                WHERE el.id = ANY(%s)
            """, [all_event_ids])
            for row in cursor.fetchall():
                id, location_id, location_name, location_address = row
                if id not in location_map:
                    location_map[id] = []
                location_map[id].append({
                    'id': location_id,
                    'name': location_name,
                    'address': location_address
                })
        
        # Prefetch all history in one query with raw SQL
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT eh.event_id, eh.modified_at, u.username
                FROM expenses_eventhistory eh
                LEFT JOIN users_user u ON eh.modified_by_id = u.id
                WHERE eh.event_id = ANY(%s)
                ORDER BY eh.modified_at DESC
            """, [all_event_ids])
            for row in cursor.fetchall():
                event_id, modified_at, username = row
                if event_id not in history_map:
                    history_map[event_id] = []
                history_map[event_id].append({
                    'modified_at': modified_at,
                    'modified_by': username
                })
        
        # Attach the prefetched data to parent events
        for parent in parent_events:
            parent.prefetched_child_events = all_child_events.get(parent.id, [])
            # Find first child's due date for parent
            if parent.prefetched_child_events:
                parent.first_child_due_date = sorted(
                    parent.prefetched_child_events,
                    key=lambda x: x.due_date
                )[0].due_date
            else:
                parent.first_child_due_date = None
            
            # Attach prefetched data to each child event
            for child in parent.prefetched_child_events:
                child.prefetched_contacts = contact_map.get(child.id, [])
                child.prefetched_locations = location_map.get(child.id, [])
                child.prefetched_history = history_map.get(child.id, [])
        
        # Attach prefetched data to normal events
        for event in normal_events:
            event.prefetched_contacts = contact_map.get(event.id, [])
            event.prefetched_locations = location_map.get(event.id, [])
            event.prefetched_history = history_map.get(event.id, [])
        
        # Create serializers with the prefetched data
        context = {'prefetched_data': True}
        parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
        normal_serializer = EventSimpleSerializer(normal_events, many=True, context=context)
        
        # Combine and sort by date
        combined_expenses = parent_serializer.data + normal_serializer.data
        combined_expenses.sort(key=lambda x: x.get('created_at', ''))
        
        # Add contact info to response
        from contacts.models.contacts import Contact
        try:
            contact = Contact.objects.get(id=contact_id)
            contact_info = {
                "id": contact_id,
                "name": contact.name,
                "email": contact.email
            }
        except:
            contact_info = {"id": contact_id}
        
        return Response({
            "contact": contact_info,
            "expenses": combined_expenses, 
            "count": len(combined_expenses)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve expenses by contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_expense(request, expense_id):
    """Soft delete an expense by setting is_deleted to True, but only if not linked to a contract or reservation"""
    try:
        with transaction.atomic():  # Wrap in transaction to ensure all or nothing
            # Get the expense with proper UUID conversion
            try:
                expense_uuid = uuid.UUID(str(expense_id))
                expense = Event.objects.select_related('contract', 'reservation', 'contact').get(id=expense_uuid)
            except (ValueError, TypeError):
                return Response(
                    {"error": f"Invalid expense ID format: {expense_id}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if expense is already deleted
            if expense.is_deleted:
                return Response(
                    {"warning": f"Expense '{expense.title}' is already marked as deleted"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the expense is linked to a contract
            if expense.contract_id is not None:
                # Return information about the associated contract without deleting the expense
                contract = expense.contract  # Already loaded via select_related
                
                return Response({
                    "warning": f"Expense '{expense.title}' was not deleted because it is linked to contract '{contract.title}'",
                    "deleted": False,
                    "contract": {
                        "id": str(contract.id),
                        "title": contract.title,
                        "start_date": contract.start_date.isoformat() if contract.start_date else None,
                        "end_date": contract.end_date.isoformat() if contract.end_date else None,
                        "status": contract.status,
                        "contact_name": contract.contact.name if hasattr(contract, 'contact') and contract.contact else None,
                    },
                    "message": f"To delete this expense, edit contract '{contract.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Check if the expense is linked to a reservation
            if expense.reservation_id is not None:
                # Return information about the associated reservation without deleting the expense
                reservation = expense.reservation  # Already loaded via select_related
                
                return Response({
                    "warning": f"Expense '{expense.title}' was not deleted because it is linked to reservation '{reservation.title}'",
                    "deleted": False,
                    "reservation": {
                        "id": str(reservation.id),
                        "title": reservation.title,
                        "start_date": reservation.start_date.isoformat() if reservation.start_date else None,
                        "end_date": reservation.end_date.isoformat() if reservation.end_date else None,
                        "status": reservation.status,
                        "contact_name": reservation.contact.name if hasattr(reservation, 'contact') and reservation.contact else None,
                    },
                    "message": f"To delete this expense, edit reservation '{reservation.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Expense is not linked to a contract or reservation, proceed with soft deletion
            
            # Capture the previous state before making changes
            previous_data = expense.to_dict()
            
            # Set is_deleted=True
            expense.is_deleted = True
            expense.save(update_fields=['is_deleted'])
            
            # Create history record with the previous state
            expense.create_history_record(previous_data, request.user)
            
            # Delete old notifications for this expense before creating new ones
            delete_notifications_by_category("expense", str(expense.id))
            
            # Create notification for the expense deletion
            create_system_notification(
                title=f"Expense '{expense.title}' Deleted",
                title_ar=f"تم حذف المصروف '{expense.title}'",
                message=f"The expense '{expense.title}' was soft-deleted by {request.user.email}",
                message_ar=f"تم حذف المصروف '{expense.title}' بواسطة {request.user.email}",
                priority="medium", 
                notification_type_name="Expense Deletion",
                category="expense",
                category_id=str(expense.id)  # Use str to ensure UUID is serialized correctly
            )
            
            return Response({
                "message": f"Expense '{expense.title}' has been marked as deleted",
                "deleted": True
            }, status=status.HTTP_200_OK)
    except Event.DoesNotExist:
        return Response(
            {"error": f"Expense with ID {expense_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to delete expense", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_event_status(request):
    """
    Update only the status of an expense event
    Request body should include event_id and status
    Optionally include paid_date when status is 'completed'
    """
    try:
        with transaction.atomic():
            # Get the event_id from request data
            event_id = request.data.get('event_id')
            if not event_id:
                return Response(
                    {"error": "event_id is required in the request body"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the event
            try:
                event = get_object_or_404(Event, id=event_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": f"Invalid event ID format: {event_id}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the new status from request data
            new_status = request.data.get('status')
            if not new_status:
                return Response(
                    {"error": "Status is required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate the status is a valid choice
            valid_statuses = [choice[0] for choice in Event.EventStatus.choices]
            if new_status not in valid_statuses:
                return Response(
                    {
                        "error": f"Invalid status value. Must be one of: {', '.join(valid_statuses)}"
                    }, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Capture the previous state before making changes
            previous_data = event.to_dict()
            
            # Update only the status
            old_status = event.status
            event.status = new_status
            
            # Handle paid_date when status is completed
            if new_status == Event.EventStatus.COMPLETED:
                # Get paid_date from request body, or use current time if not provided
                paid_date_str = request.data.get('paid_date')
                if paid_date_str:
                    try:
                        # Parse the provided paid_date
                        from django.utils.dateparse import parse_datetime
                        parsed_paid_date = parse_datetime(paid_date_str)
                        if parsed_paid_date:
                            event.paid_date = parsed_paid_date
                        else:
                            # If parsing fails, use current time
                            event.paid_date = timezone.now()
                    except Exception:
                        # If any error in parsing, use current time
                        event.paid_date = timezone.now()
                else:
                    # If no paid_date provided and no existing paid_date, set to now
                    if not event.paid_date:
                        event.paid_date = timezone.now()
            else:
                # Clear paid_date if changing from completed to another status
                if old_status == Event.EventStatus.COMPLETED and new_status != Event.EventStatus.COMPLETED:
                    event.paid_date = None
                
            # Update with current user as updater
            event.updated_by = request.user
            event.save(update_fields=['status', 'paid_date', 'updated_by'])
            
            # Create history record with the previous state
            event.create_history_record(previous_data, request.user)
            
            # Create notification for the status update
            create_system_notification(
                title=f"Expense Status Updated: {event.title}",
                title_ar=f"تم تحديث حالة المصروف: {event.title}",
                message=f"The expense '{event.title}' status was changed from '{old_status}' to '{new_status}' by {request.user.email}",
                message_ar=f"تم تغيير حالة المصروف '{event.title}' من '{old_status}' إلى '{new_status}' بواسطة {request.user.email}",
                priority="low",
                notification_type_name="Expense Status Update",
                category="expense",
                category_id=str(event.id)  # Use str to ensure UUID is serialized correctly
            )
            
            # Return detailed event info
            detail_serializer = EventDetailSerializer(event)
            return Response(detail_serializer.data)
    except Event.DoesNotExist:
        return Response(
            {"error": f"Expense with the provided ID not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update expense status", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# Internal function for direct calling from other modules
def get_expense_by_reservation_internal(reservation_id):
    """
    Internal function to retrieve all expenses associated with a specific reservation.
    
    Args:
        reservation_id: UUID of the reservation
        
    Returns:
        Dictionary with expenses data, count, and total
    """
    try:
        # Query for expenses with this reservation ID
        expenses = Event.objects.filter(
            reservation_id=reservation_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for expense in expenses:
            if expense.location:
                # Cache calculated values on the location object
                location = expense.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        
        # Serialize the data
        serializer = EventSimpleSerializer(expenses, many=True, context={'prefetched_data': True})
        data = serializer.data
        
        # Calculate the total amount
        total_amount = sum(expense.amount for expense in expenses)
        
        # Unpatch methods
        LocationPatch.unpatch(originals)
        
        # Return data
        return {
            "expenses": data,
            "count": len(data),
            "total": float(total_amount)
        }
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return {
            "expenses": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }

# Internal function for direct calling from other modules
def get_expense_by_contract_internal(contract_id):
    """
    Internal function to retrieve all expenses associated with a specific contract.
    
    Args:
        contract_id: UUID of the contract
        
    Returns:
        Dictionary with expenses data, count, and total
    """
    try:
        # Query for expenses with this contract ID
        expenses = Event.objects.filter(
            contract_id=contract_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for expense in expenses:
            if expense.location:
                # Cache calculated values on the location object
                location = expense.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        
        # Serialize the data
        serializer = EventSimpleSerializer(expenses, many=True, context={'prefetched_data': True})
        data = serializer.data
        
        # Calculate the total amount
        total_amount = sum(expense.amount for expense in expenses)
        
        # Unpatch methods
        LocationPatch.unpatch(originals)
        
        # Return data
        return {
            "expenses": data,
            "count": len(data),
            "total": float(total_amount)
        }
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return {
            "expenses": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }
    

def get_expense_by_loaction_internal(location_id):
    """
    Internal function to retrieve all expenses associated with a specific location.
    
    Args:
        location_id: UUID of the location
        
    Returns:
        Dictionary with expenses data, count, and total
    """
    try:
        # Query for expenses with this location ID
        expenses = Event.objects.filter(
            location_id=location_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for expense in expenses:
            if expense.location:
                # Cache calculated values on the location object
                location = expense.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        # Serialize the data
        serializer = EventSimpleSerializer(expenses, many=True, context={'prefetched_data': True})
        data = serializer.data
        # Calculate the total amount
        total_amount = sum(expense.amount for expense in expenses)

        # Unpatch methods
        LocationPatch.unpatch(originals)

        # Return data
        return {
            "expenses": data,
            "count": len(data),
            "total": float(total_amount)
        }
    except Exception as e:
        import traceback
        print(traceback.format_exc())

def get_expense_by_contact_internal(contact_id):
    """
    Internal function to retrieve all expenses associated with a specific contact.
    
    Args:
        contact_id: UUID of the contact
        
    Returns:
        Dictionary with expenses data, count, and total
    """
    try:
        # Query for expenses with this contact ID
        expenses = Event.objects.filter(
            contact_id=contact_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by', 'reservation', 'contract', 'income'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for expense in expenses:
            if expense.location:
                # Cache calculated values on the location object
                location = expense.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']
        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        # Serialize the data

        serializer = EventSimpleSerializer(expenses, many=True, context={'prefetched_data': True})

        data = serializer.data
        # Calculate the total amount
        total_amount = sum(expense.amount for expense in expenses)
        # Unpatch methods

        LocationPatch.unpatch(originals)
        # Return data
        return {
            "expenses": data,
            "count": len(data),
            "total": float(total_amount)
        }
    except Exception as e:
        import traceback
        print(traceback.format_exc())

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_expense_by_contract(request):
    """
    API endpoint to retrieve all expenses associated with a specific contract.
    
    Expects a POST request with contract_id in the body.
    Returns a structured response with expense data.
    """
    try:
        # Get contract_id from request body
        contract_id = request.data.get('contract_id')
        if not contract_id:
            return Response(
                {"error": "contract_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Use the internal function to get data
        result = get_expense_by_contract_internal(contract_id)
        
        # Check for errors
        if "error" in result and not result["expenses"]:
            return Response(
                {"error": "Failed to retrieve expenses", "details": result["error"]},
               
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Return response
        return Response(result)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve expenses for contract", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
