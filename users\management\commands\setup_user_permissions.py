from django.core.management.base import BaseCommand
from users.models.users import User
from users.models.permissions_base import UserAccess, ModulePermissions

class Command(BaseCommand):
    help = 'Setup UserAccess and permissions for existing users'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, help='Setup permissions for specific user email')
        parser.add_argument('--all', action='store_true', help='Setup permissions for all users without UserAccess')

    def handle(self, *args, **options):
        self.stdout.write("=== Setting up User Permissions ===")
        
        if options['email']:
            # Setup for specific user
            try:
                user = User.objects.get(email=options['email'])
                self.setup_user_permissions(user)
            except User.DoesNotExist:
                self.stdout.write(f"User with email {options['email']} not found")
        
        elif options['all']:
            # Setup for all users without UserAccess
            users_without_access = User.objects.filter(access__isnull=True)
            self.stdout.write(f"Found {users_without_access.count()} users without UserAccess")
            
            for user in users_without_access:
                self.setup_user_permissions(user)
        
        else:
            # Show users without UserAccess
            users_without_access = User.objects.filter(access__isnull=True)
            self.stdout.write(f"\nUsers without UserAccess ({users_without_access.count()}):")
            for user in users_without_access:
                self.stdout.write(f"  - {user.email} (Role: {user.role})")
            
            if users_without_access.exists():
                self.stdout.write("\nRun with --all to setup permissions for all users")
                self.stdout.write("Or use --email <email> for specific user")
    
    def setup_user_permissions(self, user):
        """Setup UserAccess and permissions for a user"""
        self.stdout.write(f"\nSetting up permissions for: {user.email}")
        
        # Create or get UserAccess
        user_access, created = UserAccess.objects.get_or_create(user=user)
        if created:
            self.stdout.write(f"  ✓ Created UserAccess")
        else:
            self.stdout.write(f"  - UserAccess already exists")
        
        # Create module permissions
        modules_created = 0
        for module_name, model_class in ModulePermissions.modules.items():
            permission, created = model_class.objects.get_or_create(user_access=user_access)
            if created:
                modules_created += 1
        
        if modules_created > 0:
            self.stdout.write(f"  ✓ Created {modules_created} module permissions")
        else:
            self.stdout.write(f"  - All module permissions already exist")
        
        self.stdout.write(f"  ✓ Setup complete for {user.email}")
