from django.urls import path
from .authentication import login, logout , refresh_token
from .users import create_user, list_users, delete_user
from .permissions import get_user_permissions, update_user_permissions, list_all_users_permissions

urlpatterns = [
    path('login/', login, name='login'),
    path('logout/', logout, name='logout'),
    path('users/create/', create_user, name='create-user'),
    path('users/list/', list_users, name='list-users'),
    path('users/delete/<int:user_id>/', delete_user, name='delete-user'),
    path('users/permissions/<int:user_id>/', get_user_permissions, name='get-user-permissions'),
    path('users/permissions/update/<int:user_id>/', update_user_permissions, name='update-user-permissions'),
    path('users/permissions/list/', list_all_users_permissions, name='list-all-users-permissions'),
    path('refresh-token/', refresh_token, name='refresh-token'),
]