from rest_framework import serializers
from django.contrib.auth import authenticate
from django.core.validators import <PERSON>ailValidator
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from ..models.users import User
from ..models.permissions_base import UserAccess
from ..serializers.permissions import UserPermissionsSerializer, UserPermissionsLightSerializer
import logging

logger = logging.getLogger(__name__)

class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(
        validators=[EmailValidator()],
        required=True
    )
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    
    def get_default_admin_permissions(self):
        """Return default full permissions for admin users"""
        base_module = {
            'sidebar': True,
            'view': True,
            'create': True,
            'edit': True,
            'delete': True,
            'export': True,
            'import': True,
            'approve': True,
            'reject': True,
            'analytics': True,
            'notifications': True,
            'view_history': True,
        }
        
        # Create permissions object with all modules
        permissions = {
            'reservations': base_module.copy(),
            'dashboard': base_module.copy(),
            'financials': base_module.copy(),
            'contacts': base_module.copy(),
            'settings': base_module.copy(),
            'income': base_module.copy(),
            'expenses': base_module.copy(),
            'calendar': base_module.copy(),
            'locations': base_module.copy(),
            'users': {**base_module.copy(), 'manage_accounts': True, 'view_activity_log': True},
            'contracts': {**base_module.copy(), 'view_terms': True, 'manage_templates': True},
        }
        
        return permissions
        
    def to_representation(self, instance):
        """Add privileges to login response"""
        try:
            logger.info(f"LoginSerializer.to_representation called for instance: {instance}")
            data = super().to_representation(instance)
            user = instance['user']
            logger.info(f"Processing permissions for user: {user.email}, is_superuser: {user.is_superuser}")

            # Check if the user is admin and add default permissions
            if user.is_superuser:
                logger.info("User is superuser, getting default admin permissions")
                data['permissions'] = self.get_default_admin_permissions()
            else:
                logger.info("User is not superuser, retrieving user permissions")
                # For non-admin users, retrieve permissions as usual
                user_access = UserAccess.objects.filter(user=user).first()
                logger.info(f"UserAccess found: {user_access is not None}")
                serializer = UserPermissionsSerializer(user_access)
                data['permissions'] = serializer.data if user_access else {}
                logger.info(f"Permissions data: {data['permissions']}")

            logger.info("LoginSerializer.to_representation completed successfully")
            return data
        except Exception as e:
            logger.error(f"Error in LoginSerializer.to_representation: {str(e)}", exc_info=True)
            raise

    def validate(self, attrs):
        try:
            logger.info("LoginSerializer.validate called")
            email = attrs.get('email')
            password = attrs.get('password')
            logger.info(f"Login attempt for email: {email}")

            if email and password:
                logger.info("Email and password provided, attempting authentication")

                # Let's check if the user exists in the database first
                from ..models.users import User
                try:
                    db_user = User.objects.get(email=email)
                    logger.info(f"User found in database: {db_user.email}, role: {db_user.role}, is_active: {db_user.is_active}, is_staff: {db_user.is_staff}, is_superuser: {db_user.is_superuser}")
                    logger.info(f"User password is set: {bool(db_user.password)}")
                except User.DoesNotExist:
                    logger.error(f"User with email {email} does not exist in database")

                user = authenticate(email=email, password=password)
                logger.info(f"Authentication result: {user is not None}")

                if not user:
                    logger.warning(f"Authentication failed for email: {email}")
                    logger.warning(f"password is: {password}")

                    # Let's check why authentication failed
                    try:
                        db_user = User.objects.get(email=email)
                        logger.error(f"User exists but authentication failed. Checking password...")
                        if db_user.check_password(password):
                            logger.error(f"Password is correct but authenticate() failed. User active: {db_user.is_active}")
                        else:
                            logger.error(f"Password check failed for user {email}")
                    except User.DoesNotExist:
                        logger.error(f"User {email} does not exist")

                    raise serializers.ValidationError(
                        "Unable to log in with provided credentials.",
                        code='authorization'
                    )

                logger.info(f"Authentication successful for user: {user.email}, role: {user.role}")
                attrs['user'] = user
            else:
                logger.error("Email or password missing in login request")
                raise serializers.ValidationError(
                    "Must include 'email' and 'password'.",
                    code='authorization'
                )

            logger.info("LoginSerializer.validate completed successfully")
            return attrs
        except Exception as e:
            logger.error(f"Error in LoginSerializer.validate: {str(e)}", exc_info=True)
            raise

class LogoutSerializer(serializers.Serializer):
    refresh = serializers.CharField(required=True)

    def validate_refresh(self, value):
        try:
            RefreshToken(value)
            return value
        except Exception as e:
            logger.error(f"Invalid refresh token in LogoutSerializer: {str(e)}")
            raise serializers.ValidationError("Invalid refresh token")

class RefreshSerializer(serializers.Serializer):
    refreshToken = serializers.CharField()

    def validate(self, attrs):
        try:
            token = RefreshToken(attrs['refreshToken'])
            attrs['accessToken'] = str(token.access_token)

            user = User.objects.get(id=token['user_id'])
            user.last_activity = timezone.now()
            user.save()

            # Get user permissions - same logic as login
            user_access = UserAccess.objects.filter(user=user).first()
            if user_access:
                permissions_serializer = UserPermissionsSerializer(user_access)
                attrs['permissions'] = permissions_serializer.data
            else:
                # If no UserAccess found, check if user is superuser or admin
                if user.is_superuser or user.role == user.Role.ADMIN:
                    # Use default admin permissions for superuser/admin without UserAccess
                    login_serializer = LoginSerializer()
                    attrs['permissions'] = login_serializer.get_default_admin_permissions()
                else:
                    attrs['permissions'] = {}

        except Exception as e:
            logger.error(f"Error in RefreshSerializer.validate: {str(e)}")
            raise serializers.ValidationError("Invalid refresh token")
        return attrs

