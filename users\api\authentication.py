from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from sarayVera.settings import numOfQueriesWraper
from ..serializers.auth import LoginSerializer, LogoutSerializer, RefreshSerializer
from ..models.permissions_base import UserAccess
from ..serializers.permissions import UserPermissionsLightSerializer , UserPermissionsSerializer
from django.db.models import Prefetch

import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@numOfQueriesWraper
def login(request):
    try:
        logger.info("Login request received")
        logger.info(f"Request data keys: {list(request.data.keys())}")

        serializer = LoginSerializer(data=request.data)
        logger.info("LoginSerializer created")

        if not serializer.is_valid():
            logger.error(f"LoginSerializer validation failed: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        logger.info("LoginSerializer validation successful")
        user = serializer.validated_data['user']
        logger.info(f"User retrieved from serializer: {user.email}, role: {user.role}")

        refresh = RefreshToken.for_user(user)
        logger.info("RefreshToken created successfully")

        # Always retrieve actual user permissions from database
        user_permissions = {}
        logger.info(f"Processing permissions for user: {user.email}, role: {user.role}, is_superuser: {user.is_superuser}")

        # First, try to get UserAccess for this user
        user_access = UserAccess.objects.filter(user=user).first()
        logger.info(f"UserAccess found: {user_access is not None}")

        if user_access:
            logger.info("Retrieving user-specific permissions from UserAccess")
            permissions_serializer = UserPermissionsSerializer(user_access)
            user_permissions = permissions_serializer.data
            logger.info("User permissions retrieved successfully from database")
        else:
            # If no UserAccess found, check if user is superuser or admin
            if user.is_superuser or user.role == user.Role.ADMIN:
                logger.info("No UserAccess found but user is superuser/admin, using default admin permissions")
                user_permissions = serializer.get_default_admin_permissions()
            else:
                logger.warning("No UserAccess found for regular user - user may need permissions setup")
                user_permissions = {}

        logger.info("Preparing response data")
        # Prepare response data including privileges
        response_data = {
            'accessToken': str(refresh.access_token),
            'refreshToken': str(refresh),
            'access_exp': refresh.access_token.lifetime.total_seconds(),
            'refresh_exp': refresh.lifetime.total_seconds(),
            'user': {
                'id': user.id,
                'email': user.email,
                'role': user.role
            },
            'permissions': user_permissions,
        }

        logger.info("Login successful, returning response")
        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Unexpected error in login view: {str(e)}", exc_info=True)
        return Response(
            {'error': 'An unexpected error occurred during login'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
def logout(request):
    try:
        logger.info("Logout request received")
        serializer = LogoutSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"LogoutSerializer validation failed: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        refresh_token = serializer.validated_data['refresh']
        logger.info("Attempting to blacklist refresh token")
        token = RefreshToken(refresh_token)
        token.blacklist()
        logger.info("Logout successful")
        return Response(status=status.HTTP_205_RESET_CONTENT)
    except Exception as e:
        logger.error(f"Error during logout: {str(e)}", exc_info=True)
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    
@api_view(['POST'])
def refresh_token(request):
    try:
        logger.info("Refresh token request received")
        serializer = RefreshSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"RefreshSerializer validation failed: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        refresh_token = serializer.validated_data['refreshToken']
        permissions = serializer.validated_data.get('permissions', {})
        logger.info("Attempting to refresh access token")

        token = RefreshToken(refresh_token)
        access_token = str(token.access_token)
        logger.info("Token refresh successful")

        return Response({
            'accessToken': access_token,
            'permissions': permissions,
            'access_exp': token.access_token.lifetime.total_seconds(),
            },
            status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error during token refresh: {str(e)}", exc_info=True)
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )