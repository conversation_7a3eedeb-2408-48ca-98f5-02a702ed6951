from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.password_validation import validate_password
from django.shortcuts import get_object_or_404
from django.db import transaction
from ..models.users import User
from ..models.permissions_base import UserAccess
from ..serializers.users import UserCreateSerializer
from ..decorators.permDecorators import check_permission

@api_view(['POST'])
def create_user(request):
    serializer = UserCreateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        validate_password(serializer.validated_data['password'])
    except Exception as e:
        return Response(
            {'password': list(e.messages)}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    user = User.objects.create_user(
        email=serializer.validated_data['email'],
        password=serializer.validated_data['password'],
        role=serializer.validated_data.get('role', User.Role.USER)
    )
    
    return Response({
        'id': user.id,
        'email': user.email,
        'role': user.role
    }, status=status.HTTP_201_CREATED)

@api_view(['GET'])
@permission_classes([IsAdminUser])
def list_users(request):
    users = User.objects.all()
    data = [{
        'id': user.id,
        'email': user.email,
        'role': user.role
    } for user in users]
    return Response(data)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@check_permission('users', 'delete')
def delete_user(request, user_id):
    """
    Delete a user by ID.

    This will:
    1. Check if the user exists
    2. Prevent deletion of the current user (self-deletion)
    3. Prevent deletion of superusers by non-superusers
    4. Delete the user and associated permissions
    """
    try:
        with transaction.atomic():
            # Get the user to delete
            user_to_delete = get_object_or_404(User, id=user_id)

            # Prevent self-deletion
            if request.user.id == user_to_delete.id:
                return Response(
                    {"error": "You cannot delete your own account"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Prevent non-superusers from deleting superusers
            if user_to_delete.is_superuser and not request.user.is_superuser:
                return Response(
                    {"error": "You don't have permission to delete admin users"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Store user info for response
            user_email = user_to_delete.email
            user_role = user_to_delete.role

            # Delete associated UserAccess and permissions (cascade will handle this)
            # The UserAccess model has CASCADE delete, so it will be automatically deleted

            # Delete the user
            user_to_delete.delete()

            return Response({
                "message": f"User '{user_email}' (role: {user_role}) has been successfully deleted"
            }, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        return Response(
            {"error": f"User with ID {user_id} not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete user", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )