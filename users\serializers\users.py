from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.hashers import make_password
from ..models.users import User
from ..models.permissions_base import UserAccess
from .permissions import UserPermissionsSerializer

class UserCreateSerializer(serializers.ModelSerializer):

    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = ['email', 'password', 'password_confirm', 'role']
        extra_kwargs = {
            'email': {'required': True},
            'role': {'default': User.Role.USER}
        }

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        
        try:
            validate_password(data['password'])
        except Exception as e:
            raise serializers.ValidationError({'password': list(e.messages)})
        
        return data

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(
            email=validated_data['email'],
            password=validated_data['password'],
            role=validated_data.get('role', User.Role.USER)
        )
        return user

class UserDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['email']
        read_only_fields = fields

class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user details (without permissions)
    """
    password = serializers.CharField(
        write_only=True,
        required=False,
        allow_blank=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = ['email', 'username', 'role', 'password', 'is_active']
        extra_kwargs = {
            'email': {'required': False},
            'username': {'required': False},
            'role': {'required': False},
            'is_active': {'required': False}
        }

    def validate(self, data):
        # Only validate password if it's provided
        if 'password' in data and data['password']:
            try:
                validate_password(data['password'])
            except Exception as e:
                raise serializers.ValidationError({'password': list(e.messages)})

        return data

    def update(self, instance, validated_data):
        # Handle password separately
        password = validated_data.pop('password', None)

        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Set password if provided
        if password:
            instance.set_password(password)

        instance.save()
        return instance

class UserWithPermissionsSerializer(serializers.Serializer):
    """
    Combined serializer for updating both user details and permissions
    """
    user = UserUpdateSerializer()
    permissions = UserPermissionsSerializer()

    def update(self, instance, validated_data):
        user_data = validated_data.get('user', {})
        permissions_data = validated_data.get('permissions', {})

        # Update user details if provided
        if user_data:
            user_serializer = UserUpdateSerializer(instance, data=user_data, partial=True)
            if user_serializer.is_valid(raise_exception=True):
                user_serializer.save()

        # Update permissions if provided
        if permissions_data:
            user_access = UserAccess.objects.get(user=instance)
            permissions_serializer = UserPermissionsSerializer(user_access, data=permissions_data, partial=True)
            if permissions_serializer.is_valid(raise_exception=True):
                permissions_serializer.save()

        return instance

    def to_representation(self, instance):
        """
        Return both user details and permissions in the response
        """
        user_access = UserAccess.objects.get(user=instance)
        return {
            'user': {
                'id': instance.id,
                'email': instance.email,
                'username': instance.username,
                'role': instance.role,
                'is_active': instance.is_active,
                'last_activity': instance.last_activity,
                'created_at': instance.created_at
            },
            'permissions': UserPermissionsSerializer(user_access).data
        }